"use client";

import * as React from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { Button } from "@/components/ui/button"
import { She<PERSON>, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Menu, Globe } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { languages, type Language } from "@/config/languages"
import logo from "@/assets/logo-with-text.png"

interface MainNavProps {
  lang: Language
  dictionary: {
    nav: {
      home: string
      services: string
      about: string
      contact: string
      bookNow: string
      resources: string
      faq: string
    }
    services: {
      individual: {
        title: string
        description: string
      }
      couples: {
        title: string
        description: string
      }
      family: {
        title: string
        description: string
      }
    }
  }
}

const ListItem = React.forwardRef<React.ElementRef<"a">, React.ComponentPropsWithoutRef<"a">>(
  ({ className, title, children, ...props }, ref) => {
    return (
      <li>
        <NavigationMenuLink asChild>
          <a
            ref={ref}
            className={cn(
              "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
              className,
            )}
            {...props}
          >
            <div className="text-sm font-medium leading-none">{title}</div>
            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">{children}</p>
          </a>
        </NavigationMenuLink>
      </li>
    )
  },
)
ListItem.displayName = "ListItem"

export function MainNav({ lang, dictionary }: MainNavProps) {
  const pathname = usePathname()
  const router = useRouter()
  const [isOpen, setIsOpen] = React.useState(false)

  // Ensure dictionary and its nested properties exist to prevent runtime errors
  const safeDict = {
    services: {
      individual: {
        title: dictionary?.services?.individual?.title || "Individual Therapy",
        description: dictionary?.services?.individual?.description || "One-on-one therapy sessions",
      },
      couples: {
        title: dictionary?.services?.couples?.title || "Couples Counseling",
        description: dictionary?.services?.couples?.description || "Strengthen your relationship",
      },
      family: {
        title: dictionary?.services?.family?.title || "Family Therapy",
        description: dictionary?.services?.family?.description || "Resolve family conflicts",
      },
    },
    nav: {
      home: dictionary?.nav?.home || "Home",
      services: dictionary?.nav?.services || "Services",
      about: dictionary?.nav?.about || "About",
      contact: dictionary?.nav?.contact || "Contact",
      bookNow: dictionary?.nav?.bookNow || "Book Now",
      resources: dictionary?.nav?.resources || "Resources",
      faq: dictionary?.nav?.faq || "FAQ",
    }
  }

  const services = [
    {
      title: safeDict.services.individual.title,
      href: `/${lang}/services/individual`,
      description: safeDict.services.individual.description,
    },
    {
      title: safeDict.services.couples.title,
      href: `/${lang}/services/couples`,
      description: safeDict.services.couples.description,
    },
    {
      title: safeDict.services.family.title,
      href: `/${lang}/services/family`,
      description: safeDict.services.family.description,
    },
  ]

  const resources = [
    {
      title: "Blog",
      href: "/blog",
      description: "Read our latest articles on mental health and wellness",
    },
    {
      title: "FAQ",
      href: "/faq",
      description: "Find answers to commonly asked questions",
    },
    {
      title: "Resources",
      href: "/resources",
      description: "Access helpful tools and materials",
    },
  ]

  const switchLanguage = (newLang: Language) => {
    let newPath = pathname

    // If the pathname starts with a language prefix, replace it
    if (newPath.startsWith(`/${lang}/`)) {
      newPath = newPath.replace(`/${lang}/`, `/${newLang}/`)
    }
    // If the pathname is just the language prefix (e.g., "/en"), replace it entirely
    else if (newPath === `/${lang}`) {
      newPath = `/${newLang}`
    }
    // If no locale is detected, add the new language prefix
    else {
      newPath = `/${newLang}${newPath}`
    }

    router.push(newPath)
    console.log(newPath)
  }


  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60" >
      <div className="container flex h-20 items-center" >
        <div className="mr-4 hidden md:flex">
          <Link href={`/${lang}`} className="me-6 flex items-center space-x-2">
            <Image
              src={logo.src}
              alt="Azad Therapy Logo"
              width={140}
              height={140}
              priority
              className="h-16 w-auto -my-2  rounded-xl bg-white"
            />
          </Link>
          <NavigationMenu dir={lang === "ar" ? "rtl" : "ltr"}>
            <NavigationMenuList className="space-x-2 rtl:space-x-reverse">
              <NavigationMenuItem>
                <Link href={`/${lang}`} legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    {safeDict.nav.home}
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link href={`/${lang}/about`} legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    {safeDict.nav.about}
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <NavigationMenuTrigger>{safeDict.nav.services}</NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                    {services.map((service) => (
                      <ListItem key={service.title} title={service.title} href={service.href}>
                        {service.description}
                      </ListItem>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link href={`/${lang}/contact`} legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    {safeDict.nav.contact}
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link href={`/${lang}/faq`} legacyBehavior passHref>
                  <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                    {safeDict.nav.faq}
                  </NavigationMenuLink>
                </Link>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
        </div>

        {/* Mobile Navigation */}
        <div className="flex md:hidden">
          <Link href="/" className="me-6 flex items-center space-x-2">
            <Image
              src="/logo.png"
              alt="Azad Therapy Logo"
              width={140}
              height={140}
              priority
              className="h-16 w-auto -my-2 rounded-xl"
            />
          </Link>
        </div>

        <div className="flex flex-1 items-center justify-end space-x-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <Globe className="h-5 w-5" />
                <span className="sr-only">Switch language</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {Object.entries(languages).map(([key, label]) => (
                <DropdownMenuItem
                  key={key}
                  onClick={() => switchLanguage(key as Language)}
                  className={cn("cursor-pointer my-1", key === lang && "bg-accent")}
                >
                  {label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <Button asChild className="hidden md:inline-flex">
            <Link href={`https://wa.me/1234567890`}>{safeDict.nav.bookNow}</Link>
          </Button>

          {/* Mobile menu button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                className="px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden"
              >
                <Menu className="h-6 w-6" />
                <span className="sr-only">Toggle Menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left">
              <div className="px-2 py-6">
                <Link href={`/${lang}`} className="flex items-center space-x-2 mb-6" onClick={() => setIsOpen(false)}>
                  <Image
                    src="/logo.png"
                    alt="Azad Therapy Logo"
                    width={140}
                    height={140}
                    className="h-16 w-auto rounded-xl"
                  />
                </Link>
                <div className="flex flex-col space-y-4">
                  <Link
                    href={`/${lang}`}
                    className="text-lg font-medium hover:text-primary"
                    onClick={() => setIsOpen(false)}
                  >
                    {safeDict.nav.home}
                  </Link>
                  <Link
                    href={`/${lang}/about`}
                    className="text-lg font-medium hover:text-primary"
                    onClick={() => setIsOpen(false)}
                  >
                    {safeDict.nav.about}
                  </Link>
                  <Link
                    href={`/${lang}/services`}
                    className="text-lg font-medium hover:text-primary"
                    onClick={() => setIsOpen(false)}
                  >
                    {safeDict.nav.services}
                  </Link>
                  <Link
                    href={`/${lang}/contact`}
                    className="text-lg font-medium hover:text-primary"
                    onClick={() => setIsOpen(false)}
                  >
                    {safeDict.nav.contact}
                  </Link>
                  <Link
                    href={`/${lang}/faq`}
                    className="text-lg font-medium hover:text-primary"
                    onClick={() => setIsOpen(false)}
                  >
                    {safeDict.nav.faq}
                  </Link>
                  <Button asChild>
                    <Link href={`https://wa.me/1234567890`}>{safeDict.nav.bookNow}</Link>
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
