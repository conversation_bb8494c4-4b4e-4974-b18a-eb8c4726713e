"use client";

import { Language } from "@/config/languages";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import Link from "next/link";

interface FAQItem {
  q: string;
  a: string;
}

interface Props {
  dictionary: any;
  lang: Language;
}

export default function FAQ({ dictionary, lang }: Props) {
  const contactPath = `/${lang}/contact`;
  // Fallback content in case the dictionary does not yet include faqPage
  const fallback: FAQItem[] = [
    {
      q: lang === "ar" ? "ما هو العلاج النفسي؟" : "What is psychotherapy?",
      a: lang === "ar"
        ? "العلاج النفسي هو عملية علاجية يتحدث فيها المريض مع أخصائي نفسي أو طبيب نفسي لمساعدته على فهم مشاعره وسلوكه."
        : "Psychotherapy is a therapeutic process where you talk with a psychologist or psychiatrist to help you understand your feelings and behaviours."
    }
  ];

  const items: FAQItem[] = dictionary?.faqPage?.items ?? fallback;
  const title: string = dictionary?.faqPage?.title ?? (lang === "ar" ? "الأسئلة الشائعة" : "Frequently Asked Questions");
  const subtitle: string = dictionary?.faqPage?.subtitle ?? (lang === "ar" ? "اعثر على إجابات لأكثر الأسئلة شيوعًا." : "Find answers to the most common questions.");
  const ctaText: string = dictionary?.faqPage?.ctaText ?? (lang === "ar" ? "ما زلت تحتاج للمزيد من المعلومات؟" : "Still need more information?");
  const ctaSub: string = lang === "ar" ? "فريقنا جاهز للإجابة على استفساراتك الشخصية في أي وقت." : "Our team is ready to answer your personal inquiries anytime.";
  const ctaButton: string = dictionary?.faqPage?.ctaButton ?? (lang === "ar" ? "اتصل بنا" : "Contact Us");

  return (
    <section className="py-20 flex justify-center">

      <div className="w-full px-4 md:w-[1200px] md:mx-auto">
        <div className="text-center mb-12">
          <span className="inline-block rounded-full bg-primary px-4 py-1 text-sm font-semibold text-white shadow-md mb-4 animate-fadeIn">{title}</span>
          <h1 className="text-4xl md:text-5xl font-extrabold mb-4 leading-tight text-foreground">
            {title}
          </h1>
          <p className="text-muted-foreground text-xl md:text-2xl max-w-2xl mx-auto">
            {subtitle}
          </p>
        </div>

        <div className="w-full ">
          <Accordion type="single" collapsible className="block w-full min-w-full space-y-4">
            {items.map((item, idx) => (
              <AccordionItem key={idx} value={String(idx)} className="w-full min-w-full">
                <AccordionTrigger className="w-full min-w-full text-lg md:text-xl font-semibold text-start break-words">
                  <div dangerouslySetInnerHTML={{ __html: item.q }}>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="text-base md:text-lg leading-relaxed text-muted-foreground whitespace-pre-line">
                  <div className="prose prose-lg prose-slate" dangerouslySetInnerHTML={{ __html: item.a }}>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>

        {/* CTA Section */}
        <div className="mt-16 rounded-xl bg-card px-6 py-10 shadow-lg flex flex-col items-center text-center space-y-6 animate-fadeIn">
          <h3 className="text-2xl md:text-3xl font-bold max-w-xl">{ctaText}</h3>
          <p className="text-muted-foreground text-lg md:text-xl max-w-xl">{ctaSub}</p>
          <Link
            href={contactPath}
            className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-lg font-medium text-white transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2"
          >
            {ctaButton}
          </Link>
        </div>
      </div>
    </section>
  );
}
