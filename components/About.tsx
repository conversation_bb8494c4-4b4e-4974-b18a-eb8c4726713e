"use client";

import { motion } from "framer-motion"
import Image from "next/image"
import { Heart, Shield, Users, CheckCircle, ArrowRight, Handshake } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import type { Language } from "@/config/languages"

interface AboutProps {
  dictionary: any // Replace `any` with a proper type if available
  lang: Language
}

export default function About({ dictionary, lang }: AboutProps) {

  const values = [
    {
      icon: <Heart className="h-12 w-12 text-secondary" />,
      title: dictionary.aboutPage.values.list[0].title,
      description: dictionary.aboutPage.values.list[0].description,
    },
    {
      icon: <Shield className="h-12 w-12 text-secondary" />,
      title: dictionary.aboutPage.values.list[1].title,
      description: dictionary.aboutPage.values.list[1].description,
    },
    {
      icon: <Users className="h-12 w-12 text-secondary" />,
      title: dictionary.aboutPage.values.list[2].title,
      description: dictionary.aboutPage.values.list[2].description,
    },
    {
      icon: <Handshake className="h-12 w-12 text-secondary" />,
      title: dictionary.aboutPage.values.list[3].title,
      description: dictionary.aboutPage.values.list[3].description,
    },
  ]

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 overflow-hidden">
      <div className="container px-4 md:px-6">
        <div className="grid gap-12 lg:grid-cols-2 lg:gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-secondary/20 to-primary/20 rounded-3xl transform rotate-3" />
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-3xl transform -rotate-3" />
            <Image
              alt="Therapy Session"
              className="relative rounded-3xl object-cover object-center shadow-xl"
              height="600"
              width="800"
              src="/therapy2.jpg?height=600&width=800"
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="space-y-4">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-primary">
                {dictionary.aboutPage.mainSection.title}
              </h2>
              <p className="text-lg text-muted-foreground max-w-[600px]">
                {dictionary.aboutPage.mainSection.description}
              </p>
            </div>

            <div className="space-y-4">
              {dictionary.aboutPage.mainSection.features.map((item: any, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-center gap-3"
                >
                  <CheckCircle className="h-5 w-5 text-secondary" />
                  <span className="text-muted-foreground">{item}</span>
                </motion.div>
              ))}
            </div>

            <Button className="group" size="lg">
              {dictionary.aboutPage.mainSection.button}
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </motion.div>
        </div>

        {/* Core Values Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="mt-24 text-center"
        >
          <h3 className="text-2xl font-bold text-primary mb-12">{dictionary.aboutPage.values.title}</h3>
          <div className="grid md:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-secondary/5 to-primary/5 rounded-xl transform transition-transform group-hover:scale-105" />
                <div className="relative space-y-4 p-6 rounded-xl">
                  <div className="flex justify-center">{value.icon}</div>
                  <h4 className="text-xl font-semibold text-primary">{value.title}</h4>
                  <p className="text-muted-foreground">{value.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Statistics Section */}
        <div className="mt-24 py-12 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-3xl">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            {dictionary.aboutPage.stats.map((stat: any, index: number) => (
              <motion.div
                key={index}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="space-y-2"
              >
                <h4 className="text-4xl font-bold text-primary">{stat.number}</h4>
                <p className="text-muted-foreground">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
