"use client";

import { motion } from "framer-motion"
import Image from "next/image"
import { Heart, Shield, Users, CheckCircle, ArrowRight, Handshake, MapPin, Brain, Lightbulb, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import type { Language } from "@/config/languages"

interface AboutProps {
  dictionary: any // Replace `any` with a proper type if available
  lang: Language
}

export default function About({ dictionary, lang }: AboutProps) {
  const isRTL = lang === "ar"

  const values = [
    {
      icon: <Shield className="h-12 w-12 text-secondary" />,
      title: dictionary.aboutPage.values.list[0].title,
      description: dictionary.aboutPage.values.list[0].description,
    },
    {
      icon: <Heart className="h-12 w-12 text-secondary" />,
      title: dictionary.aboutPage.values.list[1].title,
      description: dictionary.aboutPage.values.list[1].description,
    },
    {
      icon: <Users className="h-12 w-12 text-secondary" />,
      title: dictionary.aboutPage.values.list[2].title,
      description: dictionary.aboutPage.values.list[2].description,
    },
    {
      icon: <Handshake className="h-12 w-12 text-secondary" />,
      title: dictionary.aboutPage.values.list[3].title,
      description: dictionary.aboutPage.values.list[3].description,
    },
  ]

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 overflow-hidden">
      <div className="container px-4 md:px-6">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl font-bold tracking-tighter sm:text-6xl text-primary mb-6">
            {dictionary.aboutPage.hero.title}
          </h1>
          <h2 className="text-2xl font-semibold text-secondary mb-4">
            {dictionary.aboutPage.hero.subtitle}
          </h2>
          <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            {dictionary.aboutPage.hero.description}
          </p>
        </motion.div>

        {/* Location Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <div className="grid gap-12 lg:grid-cols-2 lg:gap-16 items-center">
            <div className={`space-y-6 ${isRTL ? 'lg:order-2' : ''}`}>
              <div className="flex items-center gap-3 mb-4">
                <MapPin className="h-8 w-8 text-secondary" />
                <h3 className="text-3xl font-bold text-primary">{dictionary.aboutPage.location.title}</h3>
              </div>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {dictionary.aboutPage.location.description}
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {dictionary.aboutPage.location.expansion}
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {dictionary.aboutPage.location.remote}
              </p>
            </div>
            <motion.div
              initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className={`relative ${isRTL ? 'lg:order-1' : ''}`}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-secondary/20 to-primary/20 rounded-3xl transform rotate-3" />
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-3xl transform -rotate-3" />
              <Image
                alt="Therapy Location"
                className="relative rounded-3xl object-cover object-center shadow-xl"
                height="400"
                width="600"
                src="/therapy2.jpg?height=400&width=600"
              />
            </motion.div>
          </div>
        </motion.div>

        {/* Philosophy Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <div className="grid gap-12 lg:grid-cols-2 lg:gap-16 items-center">
            <div className={`space-y-6 ${isRTL ? 'lg:order-2' : ''}`}>
              <motion.div
                initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-3xl transform -rotate-2" />
                <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl">
                  <Brain className="h-16 w-16 text-primary mb-6" />
                  <div className="space-y-4">
                    <div className="h-2 bg-gradient-to-r from-primary to-secondary rounded-full w-20" />
                    <div className="h-2 bg-gradient-to-r from-secondary to-primary rounded-full w-32" />
                    <div className="h-2 bg-gradient-to-r from-primary to-secondary rounded-full w-16" />
                  </div>
                </div>
              </motion.div>
            </div>
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <Lightbulb className="h-8 w-8 text-secondary" />
                <h3 className="text-3xl font-bold text-primary">{dictionary.aboutPage.philosophy.title}</h3>
              </div>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {dictionary.aboutPage.philosophy.description}
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {dictionary.aboutPage.philosophy.partnership}
              </p>
            </div>
          </div>
        </motion.div>

        {/* Team Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-primary mb-6">{dictionary.aboutPage.team.title}</h3>
            <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              {dictionary.aboutPage.team.description}
            </p>
          </div>

          <div className="bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-3xl p-8 mb-8">
            <h4 className="text-xl font-semibold text-primary mb-6 text-center">
              {dictionary.aboutPage.team.approaches.title}
            </h4>
            <div className="grid md:grid-cols-5 gap-4">
              {dictionary.aboutPage.team.approaches.list.map((approach: string, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-sm hover:shadow-md transition-shadow"
                >
                  <p className="text-sm font-medium text-primary">{approach}</p>
                </motion.div>
              ))}
            </div>
          </div>

          <div className="text-center">
            <p className="text-lg text-muted-foreground italic max-w-3xl mx-auto leading-relaxed">
              {dictionary.aboutPage.team.foundation}
            </p>
          </div>
        </motion.div>

        {/* Core Values Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="mb-16 text-center"
        >
          <h3 className="text-3xl font-bold text-primary mb-12">{dictionary.aboutPage.values.title}</h3>
          <div className="grid md:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-secondary/5 to-primary/5 rounded-xl transform transition-transform group-hover:scale-105" />
                <div className="relative space-y-4 p-6 rounded-xl">
                  <div className="flex justify-center">{value.icon}</div>
                  <h4 className="text-xl font-semibold text-primary">{value.title}</h4>
                  <p className="text-muted-foreground">{value.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Why Choose AZAD Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <div className="grid gap-12 lg:grid-cols-2 lg:gap-16 items-center">
            <div className={`space-y-6 ${isRTL ? 'lg:order-2' : ''}`}>
              <div className="flex items-center gap-3 mb-6">
                <Star className="h-8 w-8 text-secondary" />
                <h3 className="text-3xl font-bold text-primary">{dictionary.aboutPage.whyChoose.title}</h3>
              </div>
              <div className="space-y-4">
                {dictionary.aboutPage.whyChoose.reasons.map((reason: string, index: number) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start gap-3"
                  >
                    <CheckCircle className="h-5 w-5 text-secondary mt-1 flex-shrink-0" />
                    <p className="text-muted-foreground leading-relaxed">{reason}</p>
                  </motion.div>
                ))}
              </div>
            </div>
            <motion.div
              initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className={`relative ${isRTL ? 'lg:order-1' : ''}`}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-3xl transform rotate-2" />
              <div className="absolute inset-0 bg-gradient-to-br from-secondary/20 to-primary/20 rounded-3xl transform -rotate-2" />
              <Image
                alt="Why Choose AZAD"
                className="relative rounded-3xl object-cover object-center shadow-xl"
                height="400"
                width="600"
                src="/therapy2.jpg?height=400&width=600"
              />
            </motion.div>
          </div>
        </motion.div>

        {/* Statistics Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="py-12 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-3xl"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            {dictionary.aboutPage.stats.map((stat: any, index: number) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="space-y-2"
              >
                <h4 className="text-4xl font-bold text-primary">{stat.number}</h4>
                <p className="text-muted-foreground font-medium">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
