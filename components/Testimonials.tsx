"use client";
import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import Image from "next/image"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import type { Language } from "@/config/languages"

interface TestimonialsProps {
  dictionary: any // Replace `any` with a proper type if available
  lang: Language
}

export default function Testimonials({ dictionary, lang }: TestimonialsProps){
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-muted/50">
      <div className="container px-4 md:px-6">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-primary">{dictionary.testimonialsPage.title}</h2>
          <p className="mx-auto max-w-[700px] text-muted-foreground md:text-xl">
            {dictionary.testimonialsPage.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {dictionary.testimonialsPage.testimonials.map((testimonial: any, index: number) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-4">
                    {/* <div className="relative h-16 w-16 rounded-full overflow-hidden">
                      <Image
                        src={testimonial.image || "/placeholder.svg"}
                        alt={testimonial.author}
                        fill
                        className="object-cover"
                      />
                    </div> */}
                    <div>
                      <h3 className="font-semibold text-lg text-primary">{testimonial.author}</h3>
                      <p className="text-muted-foreground text-sm">{testimonial.location}</p>
                      <div className="flex items-center gap-1 mt-1">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 fill-secondary text-secondary" />
                        ))}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-4 relative">
                  <Quote className="absolute text-secondary/10 h-8 w-8 -top-1 -left-2" />
                  <blockquote className="pl-6 text-muted-foreground">"{testimonial.quote}"</blockquote>
                  <p className="mt-4 text-sm font-medium text-primary">{testimonial.role}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          {dictionary.testimonialsPage.stats.map((stat: any, index: number) => (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="space-y-2"
            >
              <h4 className="text-4xl font-bold text-primary">{stat.value}</h4>
              <p className="text-muted-foreground">{stat.label}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
