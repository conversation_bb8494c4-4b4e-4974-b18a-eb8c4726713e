"use client";
import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Calendar, MessageCircle, Shield } from "lucide-react"
import type { Language } from "@/config/languages"
import NumberCounter from "./number-counter";

interface HeroProps {
  dictionary: any // Replace `any` with a proper type if available
  lang: Language
}

export default function Hero({ dictionary, lang }: HeroProps) {
  // Create a safe dictionary with fallbacks for missing values
  const safeHero = {
    title: dictionary?.hero?.title || "Your Journey to Mental Wellbeing Starts Here",
    subtitle: dictionary?.hero?.subtitle || "Connect with licensed therapists and counselors for online therapy sessions.",
    buttons: {
      bookSession: dictionary?.hero?.buttons?.bookSession || "Book a Session",
      talkToUs: dictionary?.hero?.buttons?.talkToUs || "Talk to Us"
    },
    stats: {
      confidential: {
        value: dictionary?.hero?.stats?.confidential?.value || "100%",
        label: dictionary?.hero?.stats?.confidential?.label || "Confidential"
      },
      availability: {
        value: dictionary?.hero?.stats?.availability?.value || "24/7",
        label: dictionary?.hero?.stats?.availability?.label || "Available"
      },
      sessions: {
        value: dictionary?.hero?.stats?.sessions?.value || "12344",
        label: dictionary?.hero?.stats?.sessions?.label || "Sessions"
      }
    }
  }

  return (
    <section className="w-full min-h-[90vh] relative overflow-hidden">
      {/* Background with gradient overlay */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/therapy1.jpg?height=1080&width=1920"
          alt="Therapy session"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-primary/90 to-primary/60" />
      </div>

      <div className="container relative z-10 px-4 md:px-6">
        <div className="grid lg:grid-cols-2 gap-8 min-h-[90vh] items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-white space-y-6"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
              {safeHero.title}
            </h1>
            <p className="text-xl md:text-2xl text-zinc-100 max-w-[600px]">
              {safeHero.subtitle}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Button size="lg" asChild className="bg-white text-primary hover:bg-accent">
                <Link href="#book-session">
                  {safeHero.buttons.bookSession}
                  <Calendar className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button
                size="lg"
                variant="outline"
                asChild
                className="bg-transparent text-white border-white hover:bg-white/10"
              >
                <Link href="#contact">
                  {safeHero.buttons.talkToUs}
                  <MessageCircle className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6 text-white"
          >
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/10 rounded-full">
                <Shield className="h-6 w-6" />
              </div>
              <div>
                <h3 className="text-2xl font-bold">{safeHero.stats.confidential.value}</h3>
                <p className="text-zinc-200">{safeHero.stats.confidential.label}</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/10 rounded-full">
                <Calendar className="h-6 w-6" />
              </div>
              <div>
                <h3 className="text-2xl font-bold">{safeHero.stats.availability.value}</h3>
                <p className="text-zinc-200">{safeHero.stats.availability.label}</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/10 rounded-full">
                <MessageCircle className="h-6 w-6" />
              </div>
              <div>
                <h3 className="text-2xl font-bold flex items-center">
                  <NumberCounter number={safeHero.stats.sessions.value} />
                  +
                </h3>
                <p className="text-zinc-200">{safeHero.stats.sessions.label}</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
