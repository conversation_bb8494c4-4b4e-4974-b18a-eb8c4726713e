"use client"

import { motion } from "framer-motion"
import { MessageSquare, Mail, ArrowRight, Phone } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"
import Image from "next/image"
import type { Language } from "@/config/languages"

interface ContactProps {
  dictionary: any
  lang: Language
}

export default function Contact({ dictionary, lang }: ContactProps) {
  // Check if RTL layout (Arabic)
  const isRTL = lang === "ar"

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 border-t">
      <div className="container px-4 md:px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="flex flex-col items-center text-center space-y-4 mb-16"
        >
          <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-primary">
            {dictionary.contactPage.title}
          </h2>
          <p className="text-muted-foreground md:text-xl max-w-[600px]">{dictionary.contactPage.subtitle}</p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="p-3 rounded-full bg-secondary/10">
                    <MessageSquare className="h-10 w-10 text-secondary" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold">{dictionary.contactPage.whatsapp.title}</h3>
                    <p className="text-muted-foreground">{dictionary.contactPage.whatsapp.description}</p>
                  </div>
                  <Button asChild className="w-full mt-4 group">
                    <Link href={dictionary.contactPage.whatsapp.link}>
                      {dictionary.contactPage.whatsapp.button}
                      <ArrowRight
                        className={`${isRTL ? "mr-2 rotate-180" : "ml-2"} h-4 w-4 transition-transform group-hover:${isRTL ? "-translate-x-1" : "translate-x-1"}`}
                      />
                    </Link>
                  </Button>
                  <p className="text-sm text-muted-foreground">{dictionary.contactPage.whatsapp.availability}</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="p-3 rounded-full bg-secondary/10">
                    <Mail className="h-10 w-10 text-secondary" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold">{dictionary.contactPage.email.title}</h3>
                    <p className="text-muted-foreground">{dictionary.contactPage.email.description}</p>
                  </div>
                  <Button asChild variant="outline" className="w-full mt-4 group">
                    <Link href={dictionary.contactPage.email.link}>
                      {dictionary.contactPage.email.button}
                      <ArrowRight
                        className={`${isRTL ? "mr-2 rotate-180" : "ml-2"} h-4 w-4 transition-transform group-hover:${isRTL ? "-translate-x-1" : "translate-x-1"}`}
                      />
                    </Link>
                  </Button>
                  <p className="text-sm text-muted-foreground">{dictionary.contactPage.email.availability}</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* New Phone Numbers Component */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="p-3 rounded-full bg-secondary/10">
                    <Phone className="h-10 w-10 text-secondary" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold">
                      {(dictionary.contactPage as any).phone?.title || "Call Us"}
                    </h3>
                    <p className="text-muted-foreground">
                      {(dictionary.contactPage as any).phone?.description ||
                        "Reach out directly to our clinic via phone"}
                    </p>
                  </div>
                  <div className="space-y-2 w-full">
                    <Button asChild variant="outline" className="w-full group">
                      <Link href={`tel:${(dictionary.contactPage as any).phone?.primary || "+201211110346"}`}>
                        {(dictionary.contactPage as any).phone?.primary || "+201211110346"}
                        <ArrowRight
                          className={`${isRTL ? "mr-2 rotate-180" : "ml-2"} h-4 w-4 transition-transform group-hover:${isRTL ? "-translate-x-1" : "translate-x-1"}`}
                        />
                      </Link>
                    </Button>
                    {(dictionary.contactPage as any).phone?.secondary && (
                      <Button asChild variant="outline" className="w-full group">
                        <Link href={`tel:${(dictionary.contactPage as any).phone?.secondary || "+201000997409" }`}>
                          {(dictionary.contactPage as any).phone?.secondary || "+201000997409"}
                          <ArrowRight
                            className={`${isRTL ? "mr-2 rotate-180" : "ml-2"} h-4 w-4 transition-transform group-hover:${isRTL ? "-translate-x-1" : "translate-x-1"}`}
                          />
                        </Link>
                      </Button>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {(dictionary.contactPage as any).phone?.availability || "Available Monday-Friday, 9am-5pm"}
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Social Media Links with App Images */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-12 flex flex-col items-center"
        >
          <h3 className="text-xl font-semibold text-center mb-4">
            {((dictionary.contactPage as any).social?.title) ?? "Connect With Us"}
          </h3>
          <div className={`flex ${isRTL ? "flex-row-reverse" : "flex-row"} gap-6`}>
            <Link
              href={(dictionary.contactPage as any).social?.facebook?.link ?? "https://facebook.com/azadtherapy"}
              className="hover:opacity-80 transition-opacity duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label={((dictionary.contactPage as any).social?.facebook?.title) ?? "Facebook"}
            >
              <div className="w-12 h-12 relative">
                <Image src="/facebook_icon.png" alt="Facebook" fill className="object-contain" />
              </div>
            </Link>
            <Link
              href={(dictionary.contactPage as any).social?.instagram?.link ?? "https://instagram.com/azadtherapy"}
              className="hover:opacity-80 transition-opacity duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label={((dictionary.contactPage as any).social?.instagram?.title) ?? "Instagram"}
            >
              <div className="w-12 h-12 relative">
                <Image src="/instagram_icon.webp" alt="Instagram" fill className="object-contain" />
              </div>
            </Link>
            <Link
              href={
                (dictionary.contactPage as any).social?.linkedin?.link ?? "https://linkedin.com/company/azadtherapy"
              }
              className="hover:opacity-80 transition-opacity duration-200"
              target="_blank"
              rel="noopener noreferrer"
              aria-label={((dictionary.contactPage as any).social?.linkedin?.title) ?? "LinkedIn"}
            >
              <div className="w-12 h-12 relative">
                <Image src="/linkedin_icon.png" alt="LinkedIn" fill className="object-contain" />
              </div>
            </Link>
          </div>
        </motion.div>

        {/* Trust Indicator */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <p className="text-muted-foreground">{dictionary.contactPage.privacy}</p>
        </motion.div>
      </div>
    </section>
  )
}
