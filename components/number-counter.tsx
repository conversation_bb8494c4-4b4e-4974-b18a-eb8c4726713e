'use client';
import React, { useEffect } from "react";
import Animated<PERSON><PERSON>ber from "react-awesome-animated-number";
import "react-awesome-animated-number/dist/index.css";

type NumberCounterProps = {
  number: number;
  className?: string;
  fontStyle?: React.CSSProperties;
  includeComma?: boolean;
  locale?: string;
}

function NumberCounter(props: NumberCounterProps) {
  const { number, className, ...extras } = props;

  const [increment, setIncrement] = React.useState(0);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const randomIncrement = Math.floor(Math.random() * 10) + 1; // Random increment between 1 and 10
      setIncrement(increment + randomIncrement);
    }, 2000)

    return () => {
      clearTimeout(timeoutId);
    }
  })

  return (
    <AnimatedNumber
      value={number + increment}
      className={className + " rtl:!flex-row"}
      duration={500}
      size={24}
      {...extras}
    />
  )
}

export default NumberCounter;
