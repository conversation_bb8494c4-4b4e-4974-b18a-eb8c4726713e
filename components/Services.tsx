"use client";

import { motion } from "framer-motion"
import { Brain, Users, Heart, Sparkles, MessageCircle, Headphones, Clock, Globe, ShieldCheck, ArrowRight } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import type { Language } from "@/config/languages"
import { ReactElement, JSXElementConstructor, ReactNode, ReactPortal, AwaitedReactNode, Key } from "react";

interface ServicesProps {
  dictionary: any // Replace `any` with a proper type if available
  lang: Language
}

export default function Services({ dictionary, lang }: ServicesProps){

  const services = dictionary.servicesPage.servicesList.map((service: { title: string; }) => ({
    ...service,
    icon: getServiceIcon(service.title)
  }))

  function getServiceIcon(title: string) {
    switch (title) {
      case dictionary.servicesPage.servicesList[0].title:
        return <Brain className="h-8 w-8" />
      case dictionary.servicesPage.servicesList[1].title:
        return <Users className="h-8 w-8" />
      case dictionary.servicesPage.servicesList[2].title:
        return <Heart className="h-8 w-8" />
      case dictionary.servicesPage.servicesList[3].title:
        return <Sparkles className="h-8 w-8" />
      case dictionary.servicesPage.servicesList[4].title:
        return <MessageCircle className="h-8 w-8" />
      case dictionary.servicesPage.servicesList[5].title:
        return <Headphones className="h-8 w-8" />
      default:
        return <Brain className="h-8 w-8" />
    }
  }

  const benefits = [
    { icon: <Clock className="h-6 w-6" />, text: dictionary.servicesPage.benefits.scheduling },
    { icon: <Globe className="h-6 w-6" />, text: dictionary.servicesPage.benefits.location },
    { icon: <ShieldCheck className="h-6 w-6" />, text: dictionary.servicesPage.benefits.confidentiality },
    { icon: <Sparkles className="h-6 w-6" />, text: dictionary.servicesPage.benefits.expertise },
  ]

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-muted/30">
      <div className="container px-4 md:px-6">
        <div className="text-center space-y-4 mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-primary mb-4">
              {dictionary.servicesPage.title}
            </h2>
            <p className="text-muted-foreground text-lg max-w-[800px] mx-auto">
              {dictionary.servicesPage.subtitle}
            </p>
          </motion.div>

          {/* Service Benefits */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-12">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex flex-col items-center gap-2 p-4 rounded-lg bg-background"
              >
                <div className="p-2 rounded-full bg-secondary/10 text-secondary">
                  {benefit.icon}
                </div>
                <span className="text-sm font-medium">{benefit.text}</span>
              </motion.div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {services.map((service: any , index:number) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
                <CardHeader>
                  <div className="p-3 w-fit rounded-lg bg-secondary/10 mb-4">
                    <div className="text-secondary">
                      {service.icon}
                    </div>
                  </div>
                  <CardTitle className="text-xl mb-2">{service.title}</CardTitle>
                  <CardDescription className="text-base">
                    {service.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      {service.features.map((feature: any, i: number) => (
                        <div key={i} className="flex items-center gap-2 mb-2">
                          <div className="h-1.5 w-1.5 rounded-full bg-secondary" />
                          {feature}
                        </div>
                      ))}
                    </div>
                    <Button className="w-full group" variant="outline">
                      {dictionary.servicesPage.cta.learnMore}
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="mt-16 text-center bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-3xl p-8 md:p-12"
        >
          <h3 className="text-2xl md:text-3xl font-bold text-primary mb-4">
            {dictionary.servicesPage.cta.title}
          </h3>
          <p className="text-muted-foreground mb-6 max-w-[600px] mx-auto">
            {dictionary.servicesPage.cta.description}
          </p>
          <Button size="lg" className="group">
            {dictionary.servicesPage.cta.button}
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
