import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import Image from "next/image"
import type { Language } from "@/config/languages"

interface TeamProps {
  dictionary: any // Replace `any` with a proper type if available
  lang: Language
}

export default function Team({ dictionary, lang }: TeamProps){
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-gray-100">
      <div className="container px-4 md:px-6">
        <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl text-center mb-12">{dictionary.teamPage.title}</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {dictionary.teamPage.members.map((member: any, index: number) => (
            <Card key={index}>
              <CardHeader>
                <Image
                  src={member.image || "/placeholder.svg"}
                  alt={member.name}
                  width={200}
                  height={200}
                  className="rounded-full mx-auto"
                />
              </CardHeader>
              <CardContent className="text-center">
                <CardTitle>{member.name}</CardTitle>
                <p className="text-zinc-500">{member.role}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
