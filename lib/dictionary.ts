import type { Language } from "@/config/languages"

const dictionaries = {
  en: () => import("@/dictionaries/en.json").then((module) => module.default),
  ar: () => import("@/dictionaries/ar.json").then((module) => module.default),
}

export const getDictionary = async (locale: Language) => {
  try {
    if (!dictionaries[locale]) {
      console.warn(`No dictionary found for locale: ${locale}, falling back to English`);
      return await dictionaries.en();
    }
    
    return await dictionaries[locale]();
  } catch (error) {
    console.error(`Error loading dictionary for locale: ${locale}`, error);
    // Fallback to English in case of error
    return await dictionaries.en();
  }
};
