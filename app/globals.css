@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 212 100% 20%;

    --card: 0 0% 100%;
    --card-foreground: 212 100% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 212 100% 20%;

    --primary: 212 100% 20%;
    --primary-foreground: 0 0% 100%;

    --secondary: 191 100% 40%;
    --secondary-foreground: 212 100% 20%;

    --muted: 212 50% 95%;
    --muted-foreground: 212 40% 40%;

    --accent: 191 100% 40%;
    --accent-foreground: 212 100% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 212 30% 90%;
    --input: 212 30% 90%;
    --ring: 212 100% 20%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 212 50% 10%;
    --foreground: 0 0% 100%;

    --card: 212 50% 10%;
    --card-foreground: 0 0% 100%;

    --popover: 212 50% 10%;
    --popover-foreground: 0 0% 100%;

    --primary: 212 100% 20%;
    --primary-foreground: 0 0% 100%;

    --secondary:191 100% 40%;
    --secondary-foreground: 212 100% 20%;

    --muted: 212 40% 15%;
    --muted-foreground: 212 30% 70%;

    --accent:191 100% 40%;
    --accent-foreground: 212 100% 20%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 100%;

    --border: 212 40% 15%;
    --input: 212 40% 15%;
    --ring:191 100% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
