import About from "@/components/About"
import Team from "@/components/Team"
import { Language } from "@/config/languages"
import { getDictionary } from "@/lib/dictionary"

export default async function AboutPage({
  params: { lang },
}: {
  params: { lang: Language }
}) {
  const dictionary = await getDictionary(lang)
  return (
    <main className="flex min-h-screen flex-col items-center justify-between">
      <About dictionary={dictionary} lang={lang} />
      <Team  dictionary={dictionary} lang={lang} />
    </main>
  )
}

