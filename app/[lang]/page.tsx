import { getDictionary } from "@/lib/dictionary"
import type { Language } from "@/config/languages"
import Hero from "@/components/Hero"
import Services from "@/components/Services"
import About from "@/components/About"
import Team from "@/components/Team"
import Testimonials from "@/components/Testimonials"
import Contact from "@/components/Contact"
import { Button } from "@/components/ui/button"
import { ArrowRight, MessageSquare, Facebook, Linkedin, Instagram } from "lucide-react"
import Link from "next/link"

export default async function Home({
  params: { lang },
}: {
  params: { lang: Language }
}) {
  const dictionary = await getDictionary(lang)
  // Check if RTL layout (Arabic)
  const isRTL = lang === "ar";

  // Create a safe dictionary with fallbacks for missing values
  const safeCta = {
    title: dictionary?.cta?.title || "Ready to Start Your Journey?",
    description: dictionary?.cta?.description || "Take the first step towards better mental health today.",
    whatsapp: dictionary?.cta?.whatsapp || "Chat on WhatsApp",
    email: dictionary?.cta?.email || "Send Email"
  }

  // Get social media links from dictionary, with fallbacks
  const socialLinks = {
    title: "Connect With Us", // Hardcoded fallback
    facebook: (dictionary?.contactPage as any)?.social?.facebook?.link || "https://facebook.com/azadtherapy",
    linkedin: (dictionary?.contactPage as any)?.social?.linkedin?.link || "https://linkedin.com/company/azadtherapy",
    instagram: (dictionary?.contactPage as any)?.social?.instagram?.link || "https://instagram.com/azadtherapy"
  }

  return (
    <main className="flex min-h-screen flex-col">
      <Hero dictionary={dictionary} lang={lang} />
      <Services dictionary={dictionary} lang={lang} />
      <About dictionary={dictionary} lang={lang} />
      <Team dictionary={dictionary} lang={lang} />
      <Testimonials dictionary={dictionary} lang={lang} />
      <Contact dictionary={dictionary} lang={lang} />

      {/* CTA Section */}
      <section className="relative w-full py-16 md:py-24 lg:py-32 bg-primary overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,white_25%,white_50%,transparent_50%,transparent_75%,white_75%,white_100%)] bg-[length:60px_60px]" />
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-4 text-center">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-white max-w-3xl mx-auto">
                {safeCta.title}
              </h2>
              <p className="max-w-[600px] text-zinc-100 md:text-xl/relaxed mx-auto">{safeCta.description}</p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="bg-white text-primary hover:bg-zinc-100">
                  <Link href="https://wa.me/1234567890">
                    {safeCta.whatsapp}
                    <MessageSquare className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button
                  asChild
                  size="lg"
                  variant="outline"
                  className="bg-transparent border-white text-white hover:bg-white/10"
                >
                  <Link href="mailto:<EMAIL>">
                    {safeCta.email}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
              
              {/* Social Media Links */}
              <div className="flex flex-col items-center space-y-3 mt-8">
                <p className="text-zinc-100">{socialLinks.title}</p>
                <div className={`flex ${isRTL ? 'flex-row-reverse' : 'flex-row'} gap-4`}>
                  <Link 
                    href={socialLinks.facebook} 
                    className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors duration-200"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Facebook"
                  >
                    <Facebook className="h-5 w-5 text-white" />
                  </Link>
                  <Link 
                    href={socialLinks.instagram} 
                    className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors duration-200"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Instagram"
                  >
                    <Instagram className="h-5 w-5 text-white" />
                  </Link>
                  <Link 
                    href={socialLinks.linkedin} 
                    className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors duration-200"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="LinkedIn"
                  >
                    <Linkedin className="h-5 w-5 text-white" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
