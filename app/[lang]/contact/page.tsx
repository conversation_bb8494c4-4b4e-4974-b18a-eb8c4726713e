import Contact from "@/components/Contact"
import { Language } from "@/config/languages"
import { getDictionary } from "@/lib/dictionary"

export default async function ContactPage({
  params: { lang },
}: {
  params: { lang: Language }
}) {
  const dictionary = await getDictionary(lang)
  return (
    <main className="flex min-h-screen flex-col items-center justify-between">
      <Contact dictionary={dictionary} lang={lang} />
      </main>
  )
}

