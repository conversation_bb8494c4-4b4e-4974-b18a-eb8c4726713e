import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { MainNav } from "@/components/main-nav"
import { getDictionary } from "@/lib/dictionary"
import type { Language } from "@/config/languages"
import "../globals.css"
import FloatingWhatsApp from "@/components/FloatingWhatsApp"

import { Cairo } from "next/font/google"

const cairo = Cairo({ subsets: ["latin", "arabic"] })

export const metadata: Metadata = {
  title: " Azad Therapy Clinic",
  description: "Professional mental health care services",
  icons: "/logo_favicon.png",
}

export default async function RootLayout({
  children,
  params: { lang },
}: {
  children: React.ReactNode
  params: { lang: Language }
}) {
  const dictionary = await getDictionary(lang)

  const whatsappLink = (dictionary?.contactPage as any)?.whatsapp?.link || "https://wa.me/**********";
  const phoneNumber = whatsappLink.split('wa.me/')[1]?.split('?')[0] || "**********";
  const defaultMessage = "Hello, I'd like to inquire about therapy sessions";

  return (
    <html lang={lang} dir={lang === "ar" ? "rtl" : "ltr"}>
      <body className={cairo.className}>
        <MainNav lang={lang} dictionary={dictionary} />
        {children}
        {/* Floating WhatsApp Button */}
        <FloatingWhatsApp phoneNumber={phoneNumber} message={defaultMessage} />
        <footer className="w-full py-6 px-4 md:px-6 bg-primary text-primary-foreground">
          <div className="container mx-auto text-center">
            <p>{lang === "ar" ? "© ٢٠٢٤ عيادة آزاد للعلاج النفسي. جميع الحقوق محفوظة." : "© 2024 Azad Therapy Clinic. All rights reserved."}</p>
          </div>
        </footer>
      </body>
    </html>
  )
}