import FAQ from "@/components/FAQ";
import { Language } from "@/config/languages";
import { getDictionary } from "@/lib/dictionary";

export default async function FAQPage({
  params: { lang },
}: {
  params: { lang: Language };
}) {
  const dictionary = await getDictionary(lang);

  return (
    <main className="flex min-h-screen flex-col items-center justify-between">
      <FAQ dictionary={dictionary} lang={lang} />
    </main>
  );
}
